import 'photo_type_entity.dart';

class PhotoTypeListEntity {
  final String? taskId;
  final List<PhotoType>? photoTypes;
  final DateTime? modifiedTimeStampPhototypes;

  PhotoTypeListEntity({
    this.taskId,
    this.photoTypes,
    this.modifiedTimeStampPhototypes,
  });

  factory PhotoTypeListEntity.fromJson(Map<String, dynamic> json) {
    return PhotoTypeListEntity(
      taskId: json['task_id']?.toString(),
      photoTypes: json['photo_types'] != null
          ? (json['photo_types'] as List)
              .map((e) => PhotoType.fromJson(e))
              .toList()
          : null,
      modifiedTimeStampPhototypes:
          json['modified_time_stamp_phototypes'] != null
              ? DateTime.parse(json['modified_time_stamp_phototypes'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'task_id': taskId,
      'photo_types': photoTypes?.map((e) => e.toJson()).toList(),
      'modified_time_stamp_phototypes':
          modifiedTimeStampPhototypes?.toIso8601String(),
    };
  }
}
