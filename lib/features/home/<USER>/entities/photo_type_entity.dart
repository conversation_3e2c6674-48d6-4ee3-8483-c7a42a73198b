class PhotoType {
  final String? phototypeId;
  final String? phototypeName;
  final int? phototypePictureAmount;
  final bool? mandatory;
  final DateTime? modifiedTimeStampPhototype;

  PhotoType({
    this.phototypeId,
    this.phototypeName,
    this.phototypePictureAmount,
    this.mandatory,
    this.modifiedTimeStampPhototype,
  });

  factory PhotoType.fromJson(Map<String, dynamic> json) {
    return PhotoType(
      phototypeId: json['phototype_id']?.toString(),
      phototypeName: json['phototype_name']?.toString(),
      phototypePictureAmount: json['phototype_picture_amount']?.toInt(),
      mandatory: json['mandatory'] == true,
      modifiedTimeStampPhototype: json['modified_time_stamp_phototype'] != null
          ? DateTime.parse(json['modified_time_stamp_phototype'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'phototype_id': phototypeId,
      'phototype_name': phototypeName,
      'phototype_picture_amount': phototypePictureAmount,
      'mandatory': mandatory,
      'modified_time_stamp_phototype':
          modifiedTimeStampPhototype?.toIso8601String(),
    };
  }
}
