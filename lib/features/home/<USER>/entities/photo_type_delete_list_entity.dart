class PhotoTypeDeleteListEntity {
  final String? taskId;
  final List<String>? phototypeIdsToBeDeleted;

  PhotoTypeDeleteListEntity({
    this.taskId,
    this.phototypeIdsToBeDeleted,
  });

  factory PhotoTypeDeleteListEntity.fromJson(Map<String, dynamic> json) {
    return PhotoTypeDeleteListEntity(
      taskId: json['task_id']?.toString(),
      phototypeIdsToBeDeleted: json['phototype_ids_to_be_deleted'] != null
          ? (json['phototype_ids_to_be_deleted'] as List)
              .map((e) => e.toString())
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'task_id': taskId,
      'phototype_ids_to_be_deleted': phototypeIdsToBeDeleted,
    };
  }
}
